{"version": 3, "file": "astro.js", "sources": ["../../../../node_modules/.pnpm/@shikijs+langs@3.4.1/node_modules/@shikijs/langs/dist/astro.mjs"], "sourcesContent": ["import json from './json.mjs'\nimport javascript from './javascript.mjs'\nimport typescript from './typescript.mjs'\nimport css from './css.mjs'\nimport postcss from './postcss.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Astro\\\",\\\"fileTypes\\\":[\\\"astro\\\"],\\\"injections\\\":{\\\"L:(meta.script.astro) (meta.lang.js | meta.lang.javascript | meta.lang.partytown | meta.lang.node) - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}]},\\\"L:(meta.script.astro) (meta.lang.json) - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.json\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.json\\\"}]}]},\\\"L:(meta.script.astro) (meta.lang.ts | meta.lang.typescript) - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.ts\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}]}]},\\\"L:meta.script.astro - meta.lang - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.js\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}]},\\\"L:meta.style.astro - meta.lang - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}]}]},\\\"L:meta.style.astro meta.lang.css - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.css\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css\\\"}]}]},\\\"L:meta.style.astro meta.lang.less - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.css.less\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.less\\\"}]}]},\\\"L:meta.style.astro meta.lang.postcss - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.css.postcss\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.postcss\\\"}]}]},\\\"L:meta.style.astro meta.lang.sass - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.sass\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sass\\\"}]}]},\\\"L:meta.style.astro meta.lang.scss - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.css.scss\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.css.scss\\\"}]}]},\\\"L:meta.style.astro meta.lang.stylus - (meta source)\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=>)(?!</)\\\",\\\"contentName\\\":\\\"source.stylus\\\",\\\"end\\\":\\\"(?=</)\\\",\\\"name\\\":\\\"meta.embedded.block.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.stylus\\\"}]}]}},\\\"name\\\":\\\"astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#scope\\\"},{\\\"include\\\":\\\"#frontmatter\\\"},{\\\"include\\\":\\\"#text\\\"}],\\\"repository\\\":{\\\"attribute-literal\\\":{\\\"begin\\\":\\\"(`)\\\",\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"name\\\":\\\"string.template.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx#template-substitution-element\\\"},{\\\"include\\\":\\\"source.tsx#string-character-escape\\\"}]},\\\"attributes\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes-events\\\"},{\\\"include\\\":\\\"#attributes-keyvalue\\\"},{\\\"include\\\":\\\"#attributes-interpolated\\\"}]},\\\"attributes-events\\\":{\\\"begin\\\":\\\"(on(s(croll|t(orage|alled)|u(spend|bmit)|e(curitypolicyviolation|ek(ing|ed)|lect))|hashchange|c(hange|o(ntextmenu|py)|u(t|echange)|l(ick|ose)|an(cel|play(through)?))|t(imeupdate|oggle)|in(put|valid)|o((?:n|ff)line)|d(urationchange|r(op|ag(start|over|e(n(ter|d)|xit)|leave)?)|blclick)|un(handledrejection|load)|p(opstate|lay(ing)?|a(ste|use|ge(show|hide))|rogress)|e(nded|rror|mptied)|volumechange|key(down|up|press)|focus|w(heel|aiting)|l(oad(start|e(nd|d((?:|meta)data)))?|anguagechange)|a(uxclick|fterprint|bort)|r(e(s(ize|et)|jectionhandled)|atechange)|m(ouse(o(ut|ver)|down|up|enter|leave|move)|essage(error)?)|b(efore(unload|print)|lur)))(?![-:\\\\\\\\\\\\\\\\w])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\".*\\\",\\\"name\\\":\\\"entity.other.attribute-name.astro\\\"}]}},\\\"end\\\":\\\"(?=\\\\\\\\s*+[^=\\\\\\\\s])\\\",\\\"name\\\":\\\"meta.attribute.$1.astro\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.astro\\\"}},\\\"end\\\":\\\"(?<=[^=\\\\\\\\s])(?!\\\\\\\\s*=)|(?=/?>)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#attribute-literal\\\"},{\\\"begin\\\":\\\"(?=[^/<=>`\\\\\\\\s]|/(?!>))\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.embedded.line.js\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"source.js\\\"},\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}},\\\"match\\\":\\\"(([^\\\\\\\"'/<=>`\\\\\\\\s]|/(?!>))+)\\\",\\\"name\\\":\\\"string.unquoted.astro\\\"},{\\\"begin\\\":\\\"(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.astro\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.astro\\\"}},\\\"name\\\":\\\"string.quoted.astro\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}},\\\"match\\\":\\\"([^\\\\\\\\n\\\\\\\"/]|/(?![*/]))+\\\"},{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js\\\"}},\\\"end\\\":\\\"(?=\\\\\\\")|\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.js\\\"}},\\\"end\\\":\\\"(?=\\\\\\\")|\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.js\\\"}},\\\"name\\\":\\\"comment.block.js\\\"}]},{\\\"begin\\\":\\\"(')\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.astro\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.astro\\\"}},\\\"name\\\":\\\"string.quoted.astro\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"}]}},\\\"match\\\":\\\"([^\\\\\\\\n'/]|/(?![*/]))+\\\"},{\\\"begin\\\":\\\"//\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.js\\\"}},\\\"end\\\":\\\"(?=')|\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-slash.js\\\"},{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.js\\\"}},\\\"end\\\":\\\"(?=')|\\\\\\\\*/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.js\\\"}},\\\"name\\\":\\\"comment.block.js\\\"}]}]}]}]},\\\"attributes-interpolated\\\":{\\\"begin\\\":\\\"(?<![:=])\\\\\\\\s*(\\\\\\\\{)\\\",\\\"contentName\\\":\\\"meta.embedded.expression.astro source.tsx\\\",\\\"end\\\":\\\"(})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx\\\"}]},\\\"attributes-keyvalue\\\":{\\\"begin\\\":\\\"([$@_[:alpha:]][-$.:_[:alnum:]]*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\".*\\\",\\\"name\\\":\\\"entity.other.attribute-name.astro\\\"}]}},\\\"end\\\":\\\"(?=\\\\\\\\s*+[^=\\\\\\\\s])\\\",\\\"name\\\":\\\"meta.attribute.$1.astro\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.separator.key-value.astro\\\"}},\\\"end\\\":\\\"(?<=[^=\\\\\\\\s])(?!\\\\\\\\s*=)|(?=/?>)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes-value\\\"}]}]},\\\"attributes-value\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#interpolation\\\"},{\\\"match\\\":\\\"([^\\\\\\\"'/<=>`\\\\\\\\s]|/(?!>))+\\\",\\\"name\\\":\\\"string.unquoted.astro\\\"},{\\\"begin\\\":\\\"([\\\\\\\"'])\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.astro\\\"}},\\\"end\\\":\\\"\\\\\\\\1\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.astro\\\"}},\\\"name\\\":\\\"string.quoted.astro\\\"},{\\\"include\\\":\\\"#attribute-literal\\\"}]},\\\"comments\\\":{\\\"begin\\\":\\\"<!--\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.astro\\\"}},\\\"end\\\":\\\"-->\\\",\\\"name\\\":\\\"comment.block.astro\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\G-?>|<!--(?!>)|<!-(?=-->)|--!>\\\",\\\"name\\\":\\\"invalid.illegal.characters-not-allowed-here.astro\\\"}]},\\\"entities\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"},\\\"912\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"}},\\\"match\\\":\\\"(&)(?=[A-Za-z])((a(s(ymp(eq)?|cr|t)|n(d(slope|[dv]|and)?|g(s(t|ph)|zarr|e|le|rt(vb(d)?)?|msd(a([a-h]))?)?)|c(y|irc|d|ute|E)?|tilde|o(pf|gon)|uml|p(id|os|prox(eq)?|[Ee]|acir)?|elig|f(r)?|w((?:con|)int)|l(pha|e(ph|fsym))|acute|ring|grave|m(p|a(cr|lg))|breve)|A(s(sign|cr)|nd|MP|c(y|irc)|tilde|o(pf|gon)|uml|pplyFunction|fr|Elig|lpha|acute|ring|grave|macr|breve))|(B(scr|cy|opf|umpeq|e(cause|ta|rnoullis)|fr|a(ckslash|r(v|wed))|reve)|b(s(cr|im(e)?|ol(hsub|b)?|emi)|n(ot|e(quiv)?)|c(y|ong)|ig(s(tar|qcup)|c(irc|up|ap)|triangle(down|up)|o(times|dot|plus)|uplus|vee|wedge)|o(t(tom)?|pf|wtie|x(h([DUdu])?|times|H([DUdu])?|d([LRlr])|u([LRlr])|plus|D([LRlr])|v([HLRhlr])?|U([LRlr])|V([HLRhlr])?|minus|box))|Not|dquo|u(ll(et)?|mp(e(q)?|E)?)|prime|e(caus(e)?|t(h|ween|a)|psi|rnou|mptyv)|karow|fr|l(ock|k(1([24])|34)|a(nk|ck(square|triangle(down|left|right)?|lozenge)))|a(ck(sim(eq)?|cong|prime|epsilon)|r(vee|wed(ge)?))|r(eve|vbar)|brk(tbrk)?))|(c(s(cr|u(p(e)?|b(e)?))|h(cy|i|eck(mark)?)|ylcty|c(irc|ups(sm)?|edil|a(ps|ron))|tdot|ir(scir|c(eq|le(d(R|circ|S|dash|ast)|arrow(left|right)))?|e|fnint|E|mid)?|o(n(int|g(dot)?)|p(y(sr)?|f|rod)|lon(e(q)?)?|m(p(fn|le(xes|ment))?|ma(t)?))|dot|u(darr([lr])|p(s|c([au]p)|or|dot|brcap)?|e(sc|pr)|vee|wed|larr(p)?|r(vearrow(left|right)|ly(eq(succ|prec)|vee|wedge)|arr(m)?|ren))|e(nt(erdot)?|dil|mptyv)|fr|w((?:con|)int)|lubs(uit)?|a(cute|p(s|c([au]p)|dot|and|brcup)?|r(on|et))|r(oss|arr))|C(scr|hi|c(irc|onint|edil|aron)|ircle(Minus|Times|Dot|Plus)|Hcy|o(n(tourIntegral|int|gruent)|unterClockwiseContourIntegral|p(f|roduct)|lon(e)?)|dot|up(Cap)?|OPY|e(nterDot|dilla)|fr|lo(seCurly((?:Double|)Quote)|ckwiseContourIntegral)|a(yleys|cute|p(italDifferentialD)?)|ross))|(d(s(c([ry])|trok|ol)|har([lr])|c(y|aron)|t(dot|ri(f)?)|i(sin|e|v(ide(ontimes)?|onx)?|am(s|ond(suit)?)?|gamma)|Har|z(cy|igrarr)|o(t(square|plus|eq(dot)?|minus)?|ublebarwedge|pf|wn(harpoon(left|right)|downarrows|arrow)|llar)|d(otseq|a(rr|gger))?|u(har|arr)|jcy|e(lta|g|mptyv)|f(isht|r)|wangle|lc(orn|rop)|a(sh(v)?|leth|rr|gger)|r(c(orn|rop)|bkarow)|b(karow|lac)|Arr)|D(s(cr|trok)|c(y|aron)|Scy|i(fferentialD|a(critical(Grave|Tilde|Do(t|ubleAcute)|Acute)|mond))|o(t(Dot|Equal)?|uble(Right(Tee|Arrow)|ContourIntegral|Do(t|wnArrow)|Up((?:Down|)Arrow)|VerticalBar|L(ong(RightArrow|Left((?:Right|)Arrow))|eft(RightArrow|Tee|Arrow)))|pf|wn(Right(TeeVector|Vector(Bar)?)|Breve|Tee(Arrow)?|arrow|Left(RightVector|TeeVector|Vector(Bar)?)|Arrow(Bar|UpArrow)?))|Zcy|el(ta)?|D(otrahd)?|Jcy|fr|a(shv|rr|gger)))|(e(s(cr|im|dot)|n(sp|g)|c(y|ir(c)?|olon|aron)|t([ah])|o(pf|gon)|dot|u(ro|ml)|p(si(v|lon)?|lus|ar(sl)?)|e|D(D??ot)|q(s(im|lant(less|gtr))|c(irc|olon)|u(iv(DD)?|est|als)|vparsl)|f(Dot|r)|l(s(dot)?|inters|l)?|a(ster|cute)|r(Dot|arr)|g(s(dot)?|rave)?|x(cl|ist|p(onentiale|ectation))|m(sp(1([34]))?|pty(set|v)?|acr))|E(s(cr|im)|c(y|irc|aron)|ta|o(pf|gon)|NG|dot|uml|TH|psilon|qu(ilibrium|al(Tilde)?)|fr|lement|acute|grave|x(ists|ponentialE)|m(pty((?:|Very)SmallSquare)|acr)))|(f(scr|nof|cy|ilig|o(pf|r(k(v)?|all))|jlig|partint|emale|f(ilig|l(l??ig)|r)|l(tns|lig|at)|allingdotseq|r(own|a(sl|c(1([2-68])|78|2([35])|3([458])|45|5([68])))))|F(scr|cy|illed((?:|Very)SmallSquare)|o(uriertrf|pf|rAll)|fr))|(G(scr|c(y|irc|edil)|t|opf|dot|T|Jcy|fr|amma(d)?|reater(Greater|SlantEqual|Tilde|Equal(Less)?|FullEqual|Less)|g|breve)|g(s(cr|im([el])?)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|irc)|t(c(c|ir)|dot|quest|lPar|r(sim|dot|eq(q?less)|less|a(pprox|rr)))?|imel|opf|dot|jcy|e(s(cc|dot(o(l)?)?|l(es)?)?|q(slant|q)?|l)?|v(nE|ertneqq)|fr|E(l)?|l([Eaj])?|a(cute|p|mma(d)?)|rave|g(g)?|breve))|(h(s(cr|trok|lash)|y(phen|bull)|circ|o(ok((?:lef|righ)tarrow)|pf|arr|rbar|mtht)|e(llip|arts(uit)?|rcon)|ks([ew]arow)|fr|a(irsp|lf|r(dcy|r(cir|w)?)|milt)|bar|Arr)|H(s(cr|trok)|circ|ilbertSpace|o(pf|rizontalLine)|ump(DownHump|Equal)|fr|a(cek|t)|ARDcy))|(i(s(cr|in(s(v)?|dot|[Ev])?)|n(care|t(cal|prod|e(rcal|gers)|larhk)?|odot|fin(tie)?)?|c(y|irc)?|t(ilde)?|i(nfin|i(i??nt)|ota)?|o(cy|ta|pf|gon)|u(kcy|ml)|jlig|prod|e(cy|xcl)|quest|f([fr])|acute|grave|m(of|ped|a(cr|th|g(part|e|line))))|I(scr|n(t(e(rsection|gral))?|visible(Comma|Times))|c(y|irc)|tilde|o(ta|pf|gon)|dot|u(kcy|ml)|Ocy|Jlig|fr|Ecy|acute|grave|m(plies|a(cr|ginaryI))?))|(j(s(cr|ercy)|c(y|irc)|opf|ukcy|fr|math)|J(s(cr|ercy)|c(y|irc)|opf|ukcy|fr))|(k(scr|hcy|c(y|edil)|opf|jcy|fr|appa(v)?|green)|K(scr|c(y|edil)|Hcy|opf|Jcy|fr|appa))|(l(s(h|cr|trok|im([eg])?|q(uo(r)?|b)|aquo)|h(ar(d|u(l)?)|blk)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|ub|e(d??il)|aron)|Barr|t(hree|c(c|ir)|imes|dot|quest|larr|r(i([ef])?|Par))?|Har|o(ng(left((?:|right)arrow)|rightarrow|mapsto)|times|z(enge|f)?|oparrow(left|right)|p(f|lus|ar)|w(ast|bar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|r((?:d|us)har))|ur((?:ds|u)har)|jcy|par(lt)?|e(s(s(sim|dot|eq(q?gtr)|approx|gtr)|cc|dot(o(r)?)?|g(es)?)?|q(slant|q)?|ft(harpoon(down|up)|threetimes|leftarrows|arrow(tail)?|right(squigarrow|harpoons|arrow(s)?))|g)?|v(nE|ertneqq)|f(isht|loor|r)|E(g)?|l(hard|corner|tri|arr)?|a(ng(d|le)?|cute|t(e(s)?|ail)?|p|emptyv|quo|rr(sim|hk|tl|pl|fs|lp|b(fs)?)?|gran|mbda)|r(har(d)?|corner|tri|arr|m)|g(E)?|m(idot|oust(ache)?)|b(arr|r(k(sl([du])|e)|ac([ek]))|brk)|A(tail|arr|rr))|L(s(h|cr|trok)|c(y|edil|aron)|t|o(ng(RightArrow|left((?:|right)arrow)|rightarrow|Left((?:Right|)Arrow))|pf|wer((?:Righ|Lef)tArrow))|T|e(ss(Greater|SlantEqual|Tilde|EqualGreater|FullEqual|Less)|ft(Right(Vector|Arrow)|Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|rightarrow|Floor|A(ngleBracket|rrow(RightArrow|Bar)?)))|Jcy|fr|l(eftarrow)?|a(ng|cute|placetrf|rr|mbda)|midot))|(M(scr|cy|inusPlus|opf|u|e(diumSpace|llintrf)|fr|ap)|m(s(cr|tpos)|ho|nplus|c(y|omma)|i(nus(d(u)?|b)?|cro|d(cir|dot|ast)?)|o(dels|pf)|dash|u((?:lti|)map)?|p|easuredangle|DDot|fr|l(cp|dr)|a(cr|p(sto(down|up|left)?)?|l(t(ese)?|e)|rker)))|(n(s(hort(parallel|mid)|c(cue|[er])?|im(e(q)?)?|u(cc(eq)?|p(set(eq(q)?)?|[Ee])?|b(set(eq(q)?)?|[Ee])?)|par|qsu([bp]e)|mid)|Rightarrow|h(par|arr|Arr)|G(t(v)?|g)|c(y|ong(dot)?|up|edil|a(p|ron))|t(ilde|lg|riangle(left(eq)?|right(eq)?)|gl)|i(s(d)?|v)?|o(t(ni(v([abc]))?|in(dot|v([abc])|E)?)?|pf)|dash|u(m(sp|ero)?)?|jcy|p(olint|ar(sl|t|allel)?|r(cue|e(c(eq)?)?)?)|e(s(im|ear)|dot|quiv|ar(hk|r(ow)?)|xist(s)?|Arr)?|v(sim|infin|Harr|dash|Dash|l(t(rie)?|e|Arr)|ap|r(trie|Arr)|g([et]))|fr|w(near|ar(hk|r(ow)?)|Arr)|V([Dd]ash)|l(sim|t(ri(e)?)?|dr|e(s(s)?|q(slant|q)?|ft((?:|right)arrow))?|E|arr|Arr)|a(ng|cute|tur(al(s)?)?|p(id|os|prox|E)?|bla)|r(tri(e)?|ightarrow|arr([cw])?|Arr)|g(sim|t(r)?|e(s|q(slant|q)?)?|E)|mid|L(t(v)?|eft((?:|right)arrow)|l)|b(sp|ump(e)?))|N(scr|c(y|edil|aron)|tilde|o(nBreakingSpace|Break|t(R(ightTriangle(Bar|Equal)?|everseElement)|Greater(Greater|SlantEqual|Tilde|Equal|FullEqual|Less)?|S(u(cceeds(SlantEqual|Tilde|Equal)?|perset(Equal)?|bset(Equal)?)|quareSu(perset(Equal)?|bset(Equal)?))|Hump(DownHump|Equal)|Nested(GreaterGreater|LessLess)|C(ongruent|upCap)|Tilde(Tilde|Equal|FullEqual)?|DoubleVerticalBar|Precedes((?:Slant|)Equal)?|E(qual(Tilde)?|lement|xists)|VerticalBar|Le(ss(Greater|SlantEqual|Tilde|Equal|Less)?|ftTriangle(Bar|Equal)?))?|pf)|u|e(sted(GreaterGreater|LessLess)|wLine|gative(MediumSpace|Thi((?:n|ck)Space)|VeryThinSpace))|Jcy|fr|acute))|(o(s(cr|ol|lash)|h(m|bar)|c(y|ir(c)?)|ti(lde|mes(as)?)|S|int|opf|d(sold|iv|ot|ash|blac)|uml|p(erp|lus|ar)|elig|vbar|f(cir|r)|l(c(ir|ross)|t|ine|arr)|a(st|cute)|r(slope|igof|or|d(er(of)?|[fm])?|v|arr)?|g(t|on|rave)|m(i(nus|cron|d)|ega|acr))|O(s(cr|lash)|c(y|irc)|ti(lde|mes)|opf|dblac|uml|penCurly((?:Double|)Quote)|ver(B(ar|rac(e|ket))|Parenthesis)|fr|Elig|acute|r|grave|m(icron|ega|acr)))|(p(s(cr|i)|h(i(v)?|one|mmat)|cy|i(tchfork|v)?|o(intint|und|pf)|uncsp|er(cnt|tenk|iod|p|mil)|fr|l(us(sim|cir|two|d([ou])|e|acir|mn|b)?|an(ck(h)?|kv))|ar(s(im|l)|t|a(llel)?)?|r(sim|n(sim|E|ap)|cue|ime(s)?|o(d|p(to)?|f(surf|line|alar))|urel|e(c(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?)?|E|ap)?|m)|P(s(cr|i)|hi|cy|i|o(incareplane|pf)|fr|lusMinus|artialD|r(ime|o(duct|portion(al)?)|ecedes(SlantEqual|Tilde|Equal)?)?))|(q(scr|int|opf|u(ot|est(eq)?|at(int|ernions))|prime|fr)|Q(scr|opf|UOT|fr))|(R(s(h|cr)|ho|c(y|edil|aron)|Barr|ight(Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|Floor|A(ngleBracket|rrow(Bar|LeftArrow)?))|o(undImplies|pf)|uleDelayed|e(verse(UpEquilibrium|E(quilibrium|lement)))?|fr|EG|a(ng|cute|rr(tl)?)|rightarrow)|r(s(h|cr|q(uo(r)?|b)|aquo)|h(o(v)?|ar(d|u(l)?))|nmid|c(y|ub|e(d??il)|aron)|Barr|t(hree|imes|ri([ef]|ltri)?)|i(singdotseq|ng|ght(squigarrow|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(tail)?|rightarrows))|Har|o(times|p(f|lus|ar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|ldhar)|uluhar|p(polint|ar(gt)?)|e(ct|al(s|ine|part)?|g)|f(isht|loor|r)|l(har|arr|m)|a(ng([de]|le)?|c(ute|e)|t(io(nals)?|ail)|dic|emptyv|quo|rr(sim|hk|c|tl|pl|fs|w|lp|ap|b(fs)?)?)|rarr|x|moust(ache)?|b(arr|r(k(sl([du])|e)|ac([ek]))|brk)|A(tail|arr|rr)))|(s(s(cr|tarf|etmn|mile)|h(y|c(hcy|y)|ort(parallel|mid)|arp)|c(sim|y|n(sim|E|ap)|cue|irc|polint|e(dil)?|E|a(p|ron))?|t(ar(f)?|r(ns|aight(phi|epsilon)))|i(gma([fv])?|m(ne|dot|plus|e(q)?|l(E)?|rarr|g(E)?)?)|zlig|o(pf|ftcy|l(b(ar)?)?)|dot([be])?|u(ng|cc(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?|p(s(im|u([bp])|et(neq(q)?|eq(q)?)?)|hs(ol|ub)|1|n([Ee])|2|d(sub|ot)|3|plus|e(dot)?|E|larr|mult)?|m|b(s(im|u([bp])|et(neq(q)?|eq(q)?)?)|n([Ee])|dot|plus|e(dot)?|E|rarr|mult)?)|pa(des(uit)?|r)|e(swar|ct|tm(n|inus)|ar(hk|r(ow)?)|xt|mi|Arr)|q(su(p(set(eq)?|e)?|b(set(eq)?|e)?)|c(up(s)?|ap(s)?)|u(f|ar([ef]))?)|fr(own)?|w(nwar|ar(hk|r(ow)?)|Arr)|larr|acute|rarr|m(t(e(s)?)?|i(d|le)|eparsl|a(shp|llsetminus))|bquo)|S(scr|hort((?:Right|Down|Up|Left)Arrow)|c(y|irc|edil|aron)?|tar|igma|H(cy|CHcy)|opf|u(c(hThat|ceeds(SlantEqual|Tilde|Equal)?)|p(set|erset(Equal)?)?|m|b(set(Equal)?)?)|OFTcy|q(uare(Su(perset(Equal)?|bset(Equal)?)|Intersection|Union)?|rt)|fr|acute|mallCircle))|(t(s(hcy|c([ry])|trok)|h(i(nsp|ck(sim|approx))|orn|e(ta(sym|v)?|re(4|fore))|k(sim|ap))|c(y|edil|aron)|i(nt|lde|mes(d|b(ar)?)?)|o(sa|p(cir|f(ork)?|bot)?|ea)|dot|prime|elrec|fr|w(ixt|ohead((?:lef|righ)tarrow))|a(u|rget)|r(i(sb|time|dot|plus|e|angle(down|q|left(eq)?|right(eq)?)?|minus)|pezium|ade)|brk)|T(s(cr|trok)|RADE|h(i((?:n|ck)Space)|e(ta|refore))|c(y|edil|aron)|S(H??cy)|ilde(Tilde|Equal|FullEqual)?|HORN|opf|fr|a([bu])|ripleDot))|(u(scr|h(ar([lr])|blk)|c(y|irc)|t(ilde|dot|ri(f)?)|Har|o(pf|gon)|d(har|arr|blac)|u(arr|ml)|p(si(h|lon)?|harpoon(left|right)|downarrow|uparrows|lus|arrow)|f(isht|r)|wangle|l(c(orn(er)?|rop)|tri)|a(cute|rr)|r(c(orn(er)?|rop)|tri|ing)|grave|m(l|acr)|br(cy|eve)|Arr)|U(scr|n(ion(Plus)?|der(B(ar|rac(e|ket))|Parenthesis))|c(y|irc)|tilde|o(pf|gon)|dblac|uml|p(si(lon)?|downarrow|Tee(Arrow)?|per((?:Righ|Lef)tArrow)|DownArrow|Equilibrium|arrow|Arrow(Bar|DownArrow)?)|fr|a(cute|rr(ocir)?)|ring|grave|macr|br(cy|eve)))|(v(s(cr|u(pn([Ee])|bn([Ee])))|nsu([bp])|cy|Bar(v)?|zigzag|opf|dash|prop|e(e(eq|bar)?|llip|r(t|bar))|Dash|fr|ltri|a(ngrt|r(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|t(heta|riangle(left|right))|p(hi|i|ropto)|epsilon|kappa|r(ho)?))|rtri|Arr)|V(scr|cy|opf|dash(l)?|e(e|r(yThinSpace|t(ical(Bar|Separator|Tilde|Line))?|bar))|Dash|vdash|fr|bar))|(w(scr|circ|opf|p|e(ierp|d(ge(q)?|bar))|fr|r(eath)?)|W(scr|circ|opf|edge|fr))|(X(scr|i|opf|fr)|x(s(cr|qcup)|h([Aa]rr)|nis|c(irc|up|ap)|i|o(time|dot|p(f|lus))|dtri|u(tri|plus)|vee|fr|wedge|l([Aa]rr)|r([Aa]rr)|map))|(y(scr|c(y|irc)|icy|opf|u(cy|ml)|en|fr|ac(y|ute))|Y(scr|c(y|irc)|opf|uml|Icy|Ucy|fr|acute|Acy))|(z(scr|hcy|c(y|aron)|igrarr|opf|dot|e(ta|etrf)|fr|w(n?j)|acute)|Z(scr|c(y|aron)|Hcy|opf|dot|e(ta|roWidthSpace)|fr|acute)))(;)\\\",\\\"name\\\":\\\"constant.character.entity.named.$2.astro\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"}},\\\"match\\\":\\\"(&)#[0-9]+(;)\\\",\\\"name\\\":\\\"constant.character.entity.numeric.decimal.astro\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.entity.astro\\\"}},\\\"match\\\":\\\"(&)#[Xx]\\\\\\\\h+(;)\\\",\\\"name\\\":\\\"constant.character.entity.numeric.hexadecimal.astro\\\"},{\\\"match\\\":\\\"&(?=[0-9A-Za-z]+;)\\\",\\\"name\\\":\\\"invalid.illegal.ambiguous-ampersand.astro\\\"}]},\\\"frontmatter\\\":{\\\"begin\\\":\\\"\\\\\\\\A(-{3})\\\\\\\\s*$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment\\\"}},\\\"contentName\\\":\\\"source.ts\\\",\\\"end\\\":\\\"(^|\\\\\\\\G)(-{3})|\\\\\\\\.{3}\\\\\\\\s*$\\\",\\\"endCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"comment\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.ts\\\"}]},\\\"interpolation\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.begin.astro\\\"}},\\\"contentName\\\":\\\"meta.embedded.expression.astro source.tsx\\\",\\\"end\\\":\\\"}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.embedded.end.astro\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G\\\\\\\\s*(?=\\\\\\\\{)\\\",\\\"end\\\":\\\"(?<=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.tsx#object-literal\\\"}]},{\\\"include\\\":\\\"source.tsx\\\"}]}]},\\\"scope\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#tags\\\"},{\\\"include\\\":\\\"#interpolation\\\"},{\\\"include\\\":\\\"#entities\\\"}]},\\\"tags\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-raw\\\"},{\\\"include\\\":\\\"#tags-lang\\\"},{\\\"include\\\":\\\"#tags-void\\\"},{\\\"include\\\":\\\"#tags-general-end\\\"},{\\\"include\\\":\\\"#tags-general-start\\\"}]},\\\"tags-end-node\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.tag.end.astro punctuation.definition.tag.begin.astro\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.tag.end.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-name\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"meta.tag.end.astro punctuation.definition.tag.end.astro\\\"},\\\"4\\\":{\\\"name\\\":\\\"meta.tag.start.astro punctuation.definition.tag.end.astro\\\"}},\\\"match\\\":\\\"(</)(.*?)\\\\\\\\s*(>)|(/>)\\\"},\\\"tags-general-end\\\":{\\\"begin\\\":\\\"(</)([^/>\\\\\\\\s]*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.tag.end.astro punctuation.definition.tag.begin.astro\\\"},\\\"2\\\":{\\\"name\\\":\\\"meta.tag.end.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-name\\\"}]}},\\\"end\\\":\\\"(>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.tag.end.astro punctuation.definition.tag.end.astro\\\"}},\\\"name\\\":\\\"meta.scope.tag.$2.astro\\\"},\\\"tags-general-start\\\":{\\\"begin\\\":\\\"(<)([^/>\\\\\\\\s]*)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-start-node\\\"}]}},\\\"end\\\":\\\"(/?>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.tag.start.astro punctuation.definition.tag.end.astro\\\"}},\\\"name\\\":\\\"meta.scope.tag.$2.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-start-attributes\\\"}]},\\\"tags-lang\\\":{\\\"begin\\\":\\\"<(s(?:cript|tyle))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-start-node\\\"}]}},\\\"end\\\":\\\"</\\\\\\\\1\\\\\\\\s*>|/>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-end-node\\\"}]}},\\\"name\\\":\\\"meta.scope.tag.$1.astro meta.$1.astro\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*[^>]*?(type|lang)\\\\\\\\s*=\\\\\\\\s*([\\\\\\\"']?)(?:text/)?(application/ld\\\\\\\\+json)\\\\\\\\2)\\\",\\\"end\\\":\\\"(?=</|/>)\\\",\\\"name\\\":\\\"meta.lang.json.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-lang-start-attributes\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*[^>]*?(type|lang)\\\\\\\\s*=\\\\\\\\s*([\\\\\\\"']?)(module)\\\\\\\\2)\\\",\\\"end\\\":\\\"(?=</|/>)\\\",\\\"name\\\":\\\"meta.lang.javascript.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-lang-start-attributes\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\G(?=\\\\\\\\s*[^>]*?(type|lang)\\\\\\\\s*=\\\\\\\\s*([\\\\\\\"']?)(?:text/|application/)?([+/\\\\\\\\w]+)\\\\\\\\2)\\\",\\\"end\\\":\\\"(?=</|/>)\\\",\\\"name\\\":\\\"meta.lang.$3.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-lang-start-attributes\\\"}]},{\\\"include\\\":\\\"#tags-lang-start-attributes\\\"}]},\\\"tags-lang-start-attributes\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?=/>)|>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag.end.astro\\\"}},\\\"name\\\":\\\"meta.tag.start.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes\\\"}]},\\\"tags-name\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"[A-Z][0-9A-Z_a-z]*\\\",\\\"name\\\":\\\"support.class.component.astro\\\"},{\\\"match\\\":\\\"[a-z][0-:\\\\\\\\w]*-[-0-:\\\\\\\\w]*\\\",\\\"name\\\":\\\"meta.tag.custom.astro entity.name.tag.astro\\\"},{\\\"match\\\":\\\"[a-z][-0-:\\\\\\\\w]*\\\",\\\"name\\\":\\\"entity.name.tag.astro\\\"}]},\\\"tags-raw\\\":{\\\"begin\\\":\\\"<([^!/<>?\\\\\\\\s]+)(?=[^>]+is:raw).*?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-start-node\\\"}]}},\\\"contentName\\\":\\\"source.unknown\\\",\\\"end\\\":\\\"</\\\\\\\\1\\\\\\\\s*>|/>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-end-node\\\"}]}},\\\"name\\\":\\\"meta.scope.tag.$1.astro meta.raw.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-lang-start-attributes\\\"}]},\\\"tags-start-attributes\\\":{\\\"begin\\\":\\\"\\\\\\\\G\\\",\\\"end\\\":\\\"(?=/?>)\\\",\\\"name\\\":\\\"meta.tag.start.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes\\\"}]},\\\"tags-start-node\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.astro\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#tags-name\\\"}]}},\\\"match\\\":\\\"(<)([^/>\\\\\\\\s]*)\\\",\\\"name\\\":\\\"meta.tag.start.astro\\\"},\\\"tags-void\\\":{\\\"begin\\\":\\\"(<)(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)(?=\\\\\\\\s|/?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.astro\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.astro\\\"}},\\\"end\\\":\\\"/?>\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.tag.begin.astro\\\"}},\\\"name\\\":\\\"meta.tag.void.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#attributes\\\"}]},\\\"text\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?<=^|---|[>}])\\\",\\\"end\\\":\\\"(?=[<{]|$)\\\",\\\"name\\\":\\\"text.astro\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#entities\\\"}]}]}},\\\"scopeName\\\":\\\"source.astro\\\",\\\"embeddedLangs\\\":[\\\"json\\\",\\\"javascript\\\",\\\"typescript\\\",\\\"css\\\",\\\"postcss\\\"],\\\"embeddedLangsLazy\\\":[\\\"stylus\\\",\\\"sass\\\",\\\"scss\\\",\\\"less\\\",\\\"tsx\\\"]}\"))\n\nexport default [\n...json,\n...javascript,\n...typescript,\n...css,\n...postcss,\nlang\n]\n"], "names": ["lang", "astro", "json", "javascript", "typescript", "css", "postcss"], "mappings": "2IAMA,MAAMA,EAAO,OAAO,OAAO,KAAK,MAAM,ipuBAA6jxB,CAAC,EAErlxBC,EAAA,CACf,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACH,GAAGC,EACHN,CACA", "x_google_ignoreList": [0]}