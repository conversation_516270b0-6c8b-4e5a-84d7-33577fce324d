{"version": 3, "file": "chunk-XZIHB7SX.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.6.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n"], "names": ["ImperativeState", "_a", "init", "__name"], "mappings": "qCAKIA,GAAkBC,EAAA,KAAM,CAI1B,YAAYC,EAAM,CAChB,KAAK,KAAOA,EACZ,KAAK,QAAU,KAAK,KAAM,CAC9B,CAIE,OAAQ,CACN,KAAK,QAAU,KAAK,KAAM,CAC9B,CACA,EALIC,EAAOF,EAAM,iBAAiB,EATZA", "x_google_ignoreList": [0]}