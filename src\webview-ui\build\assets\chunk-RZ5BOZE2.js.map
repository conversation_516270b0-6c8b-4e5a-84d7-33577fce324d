{"version": 3, "file": "chunk-RZ5BOZE2.js", "sources": ["../../../../node_modules/.pnpm/mermaid@11.6.0/node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs"], "sourcesContent": ["import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n"], "names": ["getDiagramElement", "__name", "id", "securityLevel", "sandboxElement", "select", "setupViewPortForSVG", "svg", "padding", "cssDiagram", "useMaxWidth", "width", "height", "x", "y", "calculateDimensionsWithPadding", "configureSvgSize", "viewBox", "createViewBox", "log", "bounds", "_a"], "mappings": "oDAQG,IAACA,EAAoCC,EAAO,CAACC,EAAIC,IAAkB,CACpE,IAAIC,EACJ,OAAID,IAAkB,YACpBC,EAAiBC,EAAO,KAAOH,CAAE,IAEtBC,IAAkB,UAAYE,EAAOD,EAAe,QAAQ,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAO,MAAM,GAChG,OAAO,QAAQH,CAAE,IAAI,CAExC,EAAG,mBAAmB,EAGlBI,EAAsCL,EAAO,CAACM,EAAKC,EAASC,EAAYC,IAAgB,CAC1FH,EAAI,KAAK,QAASE,CAAU,EAC5B,KAAM,CAAE,MAAAE,EAAO,OAAAC,EAAQ,EAAAC,EAAG,EAAAC,CAAC,EAAKC,EAA+BR,EAAKC,CAAO,EAC3EQ,EAAiBT,EAAKK,EAAQD,EAAOD,CAAW,EAChD,MAAMO,EAAUC,EAAcL,EAAGC,EAAGH,EAAOC,EAAQJ,CAAO,EAC1DD,EAAI,KAAK,UAAWU,CAAO,EAC3BE,EAAI,MAAM,uBAAuBF,CAAO,kBAAkBT,CAAO,EAAE,CACrE,EAAG,qBAAqB,EACpBO,EAAiDd,EAAO,CAACM,EAAKC,IAAY,OAC5E,MAAMY,IAASC,EAAAd,EAAI,KAAI,IAAR,YAAAc,EAAY,YAAa,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,EAAG,EAAG,CAAG,EAC3E,MAAO,CACL,MAAOD,EAAO,MAAQZ,EAAU,EAChC,OAAQY,EAAO,OAASZ,EAAU,EAClC,EAAGY,EAAO,EACV,EAAGA,EAAO,CACX,CACH,EAAG,gCAAgC,EAC/BF,EAAgCjB,EAAO,CAACY,EAAGC,EAAGH,EAAOC,EAAQJ,IACxD,GAAGK,EAAIL,CAAO,IAAIM,EAAIN,CAAO,IAAIG,CAAK,IAAIC,CAAM,GACtD,eAAe", "x_google_ignoreList": [0]}