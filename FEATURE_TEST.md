# 新功能测试：通过 @ 符号添加选中文本到上下文

## 功能描述

这个新功能允许用户在聊天框中通过 "@" 符号快速添加当前编辑器中选中的文本到对话上下文中，类似于现有的"Add To Context"功能，但更加便捷。

## 实现的功能

1. **新的上下文菜单选项**：在 "@" 菜单中添加了"Selected Text"选项
2. **自动格式化**：选中的文本会自动格式化为与"Add To Context"相同的格式
3. **无缝集成**：与现有的 @ 提及系统完全集成

## 使用方法

1. 在 VS Code 编辑器中选中一段代码或文本
2. 在聊天框中输入 "@"
3. 从下拉菜单中选择"Selected Text"选项，或者输入"selected"、"selection"或"text"进行搜索
4. 选中的文本会自动插入到聊天框中，格式为：
   ```
   filePath:startLine-endLine
   ```
   selectedText
   ```
   ```

## 技术实现

### 前端更改

1. **ContextMenuOptionType 枚举**：添加了 `SelectedText` 选项
2. **getContextMenuOptions 函数**：在默认选项中添加了 SelectedText，并支持搜索
3. **ContextMenu 组件**：添加了对 SelectedText 的渲染和图标支持
4. **ChatTextArea 组件**：
   - 在 handleMentionSelect 中处理 SelectedText 选择
   - 添加了对 selectedTextResponse 消息的处理

### 后端更改

1. **消息类型**：
   - 在 WebviewMessage 中添加了 `getSelectedText` 类型
   - 在 ExtensionMessage 中添加了 `selectedTextResponse` 类型

2. **消息处理**：
   - 在 webviewMessageHandler 中添加了对 `getSelectedText` 的处理
   - 获取当前活动编辑器的选中文本
   - 格式化文本并发送回 webview

## 测试步骤

1. **基本功能测试**：
   - 在编辑器中选中一段代码
   - 在聊天框中输入 "@"
   - 验证"Selected Text"选项出现在菜单中
   - 选择该选项，验证文本正确插入

2. **搜索功能测试**：
   - 输入 "@selected" 验证搜索功能
   - 输入 "@selection" 验证搜索功能
   - 输入 "@text" 验证搜索功能

3. **边界情况测试**：
   - 没有选中文本时的行为
   - 没有活动编辑器时的行为
   - 选中多行文本时的格式化

4. **集成测试**：
   - 验证与其他 @ 功能的兼容性
   - 验证格式化的文本与"Add To Context"功能一致

## 预期结果

- 用户可以通过 "@" 符号快速添加选中文本到聊天上下文
- 文本格式与现有的"Add To Context"功能完全一致
- 功能与现有的 @ 提及系统无缝集成
- 提供良好的用户体验和搜索功能

## 注意事项

- 如果没有选中文本，功能会返回空响应
- 文本格式包含文件路径和行号信息
- 支持多行文本选择
- 与现有的右键菜单"Add To Context"功能互补
