const e=Object.freeze(JSON.parse(`{"displayName":"ActionScript","fileTypes":["as"],"name":"actionscript-3","patterns":[{"include":"#comments"},{"include":"#package"},{"include":"#class"},{"include":"#interface"},{"include":"#namespace_declaration"},{"include":"#import"},{"include":"#mxml"},{"include":"#strings"},{"include":"#regexp"},{"include":"#variable_declaration"},{"include":"#numbers"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#primitive_functions"},{"include":"#language_constants"},{"include":"#language_variables"},{"include":"#guess_type"},{"include":"#guess_constant"},{"include":"#other_operators"},{"include":"#arithmetic_operators"},{"include":"#logical_operators"},{"include":"#array_access_operators"},{"include":"#vector_creation_operators"},{"include":"#control_keywords"},{"include":"#other_keywords"},{"include":"#use_namespace"},{"include":"#functions"}],"repository":{"arithmetic_operators":{"match":"([-%+/]|(?<!:)\\\\*)","name":"keyword.operator.actionscript.3"},"array_access_operators":{"match":"([]\\\\[])","name":"keyword.operator.actionscript.3"},"class":{"begin":"(^|\\\\s+|;)(\\\\b(dynamic|final|abstract)\\\\b\\\\s+)?(\\\\b(internal|public)\\\\b\\\\s+)?(\\\\b(dynamic|final|abstract)\\\\b\\\\s+)?(?=\\\\bclass\\\\b)","beginCaptures":{"3":{"name":"storage.modifier.actionscript.3"},"5":{"name":"storage.modifier.actionscript.3"},"7":{"name":"storage.modifier.actionscript.3"}},"end":"}","name":"meta.class.actionscript.3","patterns":[{"include":"#class_declaration"},{"include":"#metadata"},{"include":"#method"},{"include":"#comments"},{"include":"#strings"},{"include":"#regexp"},{"include":"#numbers"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#primitive_functions"},{"include":"#language_constants"},{"include":"#language_variables"},{"include":"#other_operators"},{"include":"#other_keywords"},{"include":"#use_namespace"},{"include":"#guess_type"},{"include":"#guess_constant"},{"include":"#arithmetic_operators"},{"include":"#array_access_operators"},{"include":"#vector_creation_operators"},{"include":"#variable_declaration"},{"include":"#object_literal"}]},"class_declaration":{"begin":"\\\\b(class)\\\\b\\\\s+([$.0-9A-Z_a-z]+|\\\\*)","beginCaptures":{"1":{"name":"storage.type.class.actionscript.3"},"2":{"name":"entity.name.class.actionscript.3"}},"end":"\\\\{","name":"meta.class_declaration.actionscript.3","patterns":[{"include":"#extends"},{"include":"#implements"},{"include":"#comments"}]},"code_block":{"begin":"\\\\{","end":"}","name":"meta.code_block.actionscript.3","patterns":[{"include":"#code_block"},{"include":"#comments"},{"include":"#strings"},{"include":"#regexp"},{"include":"#variable_declaration"},{"include":"#numbers"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#primitive_functions"},{"include":"#language_constants"},{"include":"#language_variables"},{"include":"#guess_type"},{"include":"#guess_constant"},{"include":"#other_operators"},{"include":"#arithmetic_operators"},{"include":"#logical_operators"},{"include":"#array_access_operators"},{"include":"#vector_creation_operators"},{"include":"#control_keywords"},{"include":"#other_keywords"},{"include":"#use_namespace"},{"include":"#functions"},{"include":"#import"}]},"comments":{"patterns":[{"begin":"/\\\\*\\\\*(?!/)","end":"\\\\*/","name":"comment.block.documentation.actionscript.3","patterns":[{"match":"@(copy|default|eventType|example|exampleText|includeExample|inheritDoc|internal|param|private|return|see|since|throws)\\\\b","name":"keyword.other.documentation.actionscript.3.asdoc"}]},{"begin":"/\\\\*","end":"\\\\*/","name":"comment.block.actionscript.3"},{"match":"//.*","name":"comment.line.actionscript.3"}]},"control_keywords":{"match":"\\\\b(if|else|do|while|for|each|continue|return|switch|case|default|break|try|catch|finally|throw|with)\\\\b","name":"keyword.control.actionscript.3"},"dynamic_type":{"captures":{"1":{"name":"support.type.actionscript.3"}},"match":"(?<=:)\\\\s*(\\\\*)"},"escapes":{"match":"\\\\\\\\(x\\\\h{2}|[012][0-7]{0,2}|3[0-6][0-7]|37[0-7]?|[4-7][0-7]?|.)","name":"constant.character.escape.actionscript.3"},"extends":{"captures":{"1":{"name":"keyword.other.actionscript.3"},"2":{"name":"entity.other.inherited-class.actionscript.3"},"3":{"name":"entity.other.inherited-class.actionscript.3"}},"match":"\\\\b(extends)\\\\b\\\\s+([$.0-9A-Z_a-z]+)\\\\s*(?:,\\\\s*([$.0-9A-Z_a-z]+))*\\\\s*","name":"meta.extends.actionscript.3"},"function_arguments":{"begin":"\\\\(","end":"\\\\)","name":"meta.function_arguments.actionscript.3","patterns":[{"include":"#parameters"},{"include":"#comments"}]},"functions":{"begin":"\\\\b(function)\\\\b(?:\\\\s+\\\\b([gs]et)\\\\b\\\\s+)?\\\\s*([$0-9A-Z_a-z]+\\\\b)?","beginCaptures":{"1":{"name":"storage.type.function.actionscript.3"},"2":{"name":"storage.modifier.actionscript.3"},"3":{"name":"entity.name.function.actionscript.3"}},"end":"($|;|(?=\\\\{))","name":"meta.function.actionscript.3","patterns":[{"include":"#function_arguments"},{"include":"#return_type"},{"include":"#comments"}]},"guess_constant":{"captures":{"1":{"name":"constant.other.actionscript.3"}},"match":"\\\\b([$A-Z][0-9A-Z_]+)\\\\b"},"guess_type":{"captures":{"1":{"name":"support.type.actionscript.3"}},"match":"\\\\b((?:[$0-9A-Z_a-z]+\\\\.)*[A-Z][0-9A-Z]*[a-z]+[$0-9A-Z_a-z]*)\\\\b"},"implements":{"captures":{"1":{"name":"keyword.other.actionscript.3"},"2":{"name":"entity.other.inherited-class.actionscript.3"},"3":{"name":"entity.other.inherited-class.actionscript.3"}},"match":"\\\\b(implements)\\\\b\\\\s+([$.0-9A-Z_a-z]+)\\\\s*(?:,\\\\s*([$.0-9A-Z_a-z]+))*\\\\s*","name":"meta.implements.actionscript.3"},"import":{"captures":{"2":{"name":"keyword.control.import.actionscript.3"},"3":{"name":"support.type.actionscript.3"}},"match":"(^|\\\\s+|;)\\\\b(import)\\\\b\\\\s+([$.0-9A-Z_a-z]+(?:\\\\.\\\\*)?)\\\\s*(?=;|$)","name":"meta.import.actionscript.3"},"interface":{"begin":"(^|\\\\s+|;)(\\\\b(internal|public)\\\\b\\\\s+)?(?=\\\\binterface\\\\b)","beginCaptures":{"3":{"name":"storage.modifier.actionscript.3"}},"end":"}","name":"meta.interface.actionscript.3","patterns":[{"include":"#interface_declaration"},{"include":"#metadata"},{"include":"#functions"},{"include":"#comments"}]},"interface_declaration":{"begin":"\\\\b(interface)\\\\b\\\\s+([$.0-9A-Z_a-z]+)","beginCaptures":{"1":{"name":"storage.type.interface.actionscript.3"},"2":{"name":"entity.name.class.actionscript.3"}},"end":"\\\\{","name":"meta.class_declaration.actionscript.3","patterns":[{"include":"#extends"},{"include":"#comments"}]},"language_constants":{"match":"\\\\b(true|false|null|Infinity|-Infinity|NaN|undefined)\\\\b","name":"constant.language.actionscript.3"},"language_variables":{"match":"\\\\b(super|this|arguments)\\\\b","name":"variable.language.actionscript.3"},"logical_operators":{"match":"([!\\\\&<>?^|~])","name":"keyword.operator.actionscript.3"},"metadata":{"begin":"(?<=(?:^|[;{}]|\\\\*/)\\\\s*)\\\\[\\\\s*\\\\b([$A-Z_a-z][$0-9A-Z_a-z]+)\\\\b","beginCaptures":{"1":{"name":"keyword.other.actionscript.3"}},"end":"]","name":"meta.metadata_info.actionscript.3","patterns":[{"include":"#metadata_info"}]},"metadata_info":{"begin":"\\\\(","end":"\\\\)","patterns":[{"include":"#strings"},{"captures":{"1":{"name":"variable.parameter.actionscript.3"},"2":{"name":"keyword.operator.actionscript.3"}},"match":"(\\\\w+)\\\\s*(=)"}]},"method":{"begin":"(^|\\\\s+)((\\\\w+)\\\\s+)?((\\\\w+)\\\\s+)?((\\\\w+)\\\\s+)?((\\\\w+)\\\\s+)?(?=\\\\bfunction\\\\b)","beginCaptures":{"3":{"name":"storage.modifier.actionscript.3"},"5":{"name":"storage.modifier.actionscript.3"},"7":{"name":"storage.modifier.actionscript.3"},"8":{"name":"storage.modifier.actionscript.3"}},"end":"(?<=([;}]))","name":"meta.method.actionscript.3","patterns":[{"include":"#functions"},{"include":"#code_block"}]},"mxml":{"begin":"<!\\\\[CDATA\\\\[","end":"]]>","name":"meta.cdata.actionscript.3","patterns":[{"include":"#comments"},{"include":"#import"},{"include":"#metadata"},{"include":"#class"},{"include":"#namespace_declaration"},{"include":"#use_namespace"},{"include":"#class_declaration"},{"include":"#method"},{"include":"#comments"},{"include":"#strings"},{"include":"#regexp"},{"include":"#numbers"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#primitive_functions"},{"include":"#language_constants"},{"include":"#language_variables"},{"include":"#other_keywords"},{"include":"#guess_type"},{"include":"#guess_constant"},{"include":"#other_operators"},{"include":"#arithmetic_operators"},{"include":"#array_access_operators"},{"include":"#vector_creation_operators"},{"include":"#variable_declaration"}]},"namespace_declaration":{"captures":{"2":{"name":"storage.modifier.actionscript.3"},"3":{"name":"storage.modifier.actionscript.3"}},"match":"((\\\\w+)\\\\s+)?(namespace)\\\\s+[$0-9A-Z_a-z]+","name":"meta.namespace_declaration.actionscript.3"},"numbers":{"match":"\\\\b((0([Xx])\\\\h*)|(([0-9]+\\\\.?[0-9]*)|(\\\\.[0-9]+))(([Ee])([-+])?[0-9]+)?)([Ll]|UL|ul|[FUfu])?\\\\b","name":"constant.numeric.actionscript.3"},"object_literal":{"begin":"\\\\{","end":"}","name":"meta.object_literal.actionscript.3","patterns":[{"include":"#object_literal"},{"include":"#comments"},{"include":"#strings"},{"include":"#regexp"},{"include":"#numbers"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#primitive_functions"},{"include":"#language_constants"},{"include":"#language_variables"},{"include":"#guess_type"},{"include":"#guess_constant"},{"include":"#array_access_operators"},{"include":"#vector_creation_operators"},{"include":"#functions"}]},"other_keywords":{"match":"\\\\b(as|delete|in|instanceof|is|native|new|to|typeof)\\\\b","name":"keyword.other.actionscript.3"},"other_operators":{"match":"([.=])","name":"keyword.operator.actionscript.3"},"package":{"begin":"(^|\\\\s+)(package)\\\\b","beginCaptures":{"2":{"name":"keyword.other.actionscript.3"}},"end":"}","name":"meta.package.actionscript.3","patterns":[{"include":"#package_name"},{"include":"#variable_declaration"},{"include":"#method"},{"include":"#comments"},{"include":"#return_type"},{"include":"#import"},{"include":"#use_namespace"},{"include":"#strings"},{"include":"#numbers"},{"include":"#language_constants"},{"include":"#metadata"},{"include":"#class"},{"include":"#interface"},{"include":"#namespace_declaration"}]},"package_name":{"begin":"(?<=package)\\\\s+([._\\\\w]*)\\\\b","end":"\\\\{","name":"meta.package_name.actionscript.3"},"parameters":{"begin":"(\\\\.\\\\.\\\\.)?\\\\s*([$A-Z_a-z][$0-9A-Z_a-z]*)(?:\\\\s*(:)\\\\s*(?:([$A-Za-z][$0-9A-Z_a-z]+(?:\\\\.[$A-Za-z][$0-9A-Z_a-z]+)*)(?:\\\\.<([$A-Za-z][$0-9A-Z_a-z]+(?:\\\\.[$A-Za-z][$0-9A-Z_a-z]+)*)>)?|(\\\\*)))?(?:\\\\s*(=))?","beginCaptures":{"1":{"name":"keyword.operator.actionscript.3"},"2":{"name":"variable.parameter.actionscript.3"},"3":{"name":"keyword.operator.actionscript.3"},"4":{"name":"support.type.actionscript.3"},"5":{"name":"support.type.actionscript.3"},"6":{"name":"support.type.actionscript.3"},"7":{"name":"keyword.operator.actionscript.3"}},"end":",|(?=\\\\))","patterns":[{"include":"#strings"},{"include":"#numbers"},{"include":"#language_constants"},{"include":"#comments"},{"include":"#primitive_types"},{"include":"#primitive_error_types"},{"include":"#dynamic_type"},{"include":"#guess_type"},{"include":"#guess_constant"}]},"primitive_error_types":{"captures":{"1":{"name":"support.class.error.actionscript.3"}},"match":"\\\\b((Argument|Definition|Eval|Internal|Range|Reference|Security|Syntax|Type|URI|Verify)?Error)\\\\b"},"primitive_functions":{"captures":{"1":{"name":"support.function.actionscript.3"}},"match":"\\\\b(decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|escape|isFinite|isNaN|isXMLName|parseFloat|parseInt|trace|unescape)(?=\\\\s*\\\\()"},"primitive_types":{"captures":{"1":{"name":"support.class.builtin.actionscript.3"}},"match":"\\\\b(Array|Boolean|Class|Date|Function|int|JSON|Math|Namespace|Number|Object|QName|RegExp|String|uint|Vector|XML|XMLList|\\\\*(?<=a))\\\\b"},"regexp":{"begin":"(?<=[(,:=\\\\[]|^|return|&&|\\\\|\\\\||!)\\\\s*(/)(?![*+/?{}])","end":"$|(/)[gim]*","name":"string.regex.actionscript.3","patterns":[{"match":"\\\\\\\\.","name":"constant.character.escape.actionscript.3"},{"match":"\\\\[(\\\\\\\\]|[^]])*]","name":"constant.character.class.actionscript.3"}]},"return_type":{"captures":{"1":{"name":"keyword.operator.actionscript.3"},"2":{"name":"support.type.actionscript.3"},"3":{"name":"support.type.actionscript.3"},"4":{"name":"support.type.actionscript.3"}},"match":"(:)\\\\s*([$A-Za-z][$0-9A-Z_a-z]+(?:\\\\.[$A-Za-z][$0-9A-Z_a-z]+)*)(?:\\\\.<([$A-Za-z][$0-9A-Z_a-z]+(?:\\\\.[$A-Za-z][$0-9A-Z_a-z]+)*)>)?|(\\\\*)"},"strings":{"patterns":[{"begin":"@\\"","end":"\\"","name":"string.quoted.verbatim.actionscript.3"},{"begin":"\\"","end":"\\"","name":"string.quoted.double.actionscript.3","patterns":[{"include":"#escapes"}]},{"begin":"'","end":"'","name":"string.quoted.single.actionscript.3","patterns":[{"include":"#escapes"}]}]},"use_namespace":{"captures":{"2":{"name":"keyword.other.actionscript.3"},"3":{"name":"keyword.other.actionscript.3"},"4":{"name":"storage.modifier.actionscript.3"}},"match":"(^|\\\\s+|;)(use\\\\s+)?(namespace)\\\\s+(\\\\w+)\\\\s*(;|$)"},"variable_declaration":{"captures":{"2":{"name":"storage.modifier.actionscript.3"},"4":{"name":"storage.modifier.actionscript.3"},"6":{"name":"storage.modifier.actionscript.3"},"7":{"name":"storage.modifier.actionscript.3"},"8":{"name":"keyword.operator.actionscript.3"}},"match":"((static)\\\\s+)?((\\\\w+)\\\\s+)?((static)\\\\s+)?(const|var)\\\\s+[$0-9A-Z_a-z]+(?:\\\\s*(:))?","name":"meta.variable_declaration.actionscript.3"},"vector_creation_operators":{"match":"([<>])","name":"keyword.operator.actionscript.3"}},"scopeName":"source.actionscript.3"}`)),t=[e];export{t as default};
//# sourceMappingURL=actionscript-3.js.map
