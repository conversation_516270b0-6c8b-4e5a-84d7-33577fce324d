name: Code QA Roo Code

on:
  workflow_dispatch:
  push:
    branches: [main]
  pull_request:
    types: [opened, reopened, ready_for_review, synchronize]
    branches: [main]

env:
  NODE_VERSION: 20.18.1
  PNPM_VERSION: 10.8.1

jobs:
  check-translations:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Verify all translations are complete
        run: node scripts/find-missing-translations.js

  knip:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Run knip checks
        run: pnpm knip

  compile:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Lint
        run: pnpm lint
      - name: Check types
        run: pnpm check-types

  platform-unit-test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Run unit tests
        run: pnpm test

  check-openrouter-api-key:
    runs-on: ubuntu-latest
    outputs:
      exists: ${{ steps.openrouter-api-key-check.outputs.defined }}
    steps:
      - name: Check if OpenRouter API key exists
        id: openrouter-api-key-check
        shell: bash
        run: |
          if [ "${{ secrets.OPENROUTER_API_KEY }}" != '' ]; then
            echo "defined=true" >> $GITHUB_OUTPUT;
          else
            echo "defined=false" >> $GITHUB_OUTPUT;
          fi

  integration-test:
    runs-on: ubuntu-latest
    needs: [check-openrouter-api-key]
    if: needs.check-openrouter-api-key.outputs.exists == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Create .env.local file
        working-directory: apps/vscode-e2e
        run: echo "OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY }}" > .env.local
      - name: Run integration tests
        working-directory: apps/vscode-e2e
        run: xvfb-run -a pnpm test:ci
