const e=Object.freeze(JSON.parse('{"displayName":"MIPS Assembly","fileTypes":["s","mips","spim","asm"],"name":"mipsasm","patterns":[{"match":"\\\\b(mul|abs|divu??|mulou??|negu??|not|remu??|rol|ror|li|seq|sgeu??|sgtu??|sleu??|sne|b|beqz|bgeu??|bgtu??|bleu??|bltu??|bnez|la|ld|ulhu??|ulw|sd|ush|usw|move|mfc1\\\\.d|l\\\\.d|l\\\\.s|s\\\\.d|s\\\\.s)\\\\b","name":"support.function.pseudo.mips"},{"match":"\\\\b(abs\\\\.d|abs\\\\.s|add|add\\\\.d|add\\\\.s|addiu??|addu|andi??|bc1f|bc1t|beq|bgez|bgezal|bgtz|blez|bltz|bltzal|bne|break|c\\\\.eq\\\\.d|c\\\\.eq\\\\.s|c\\\\.le\\\\.d|c\\\\.le\\\\.s|c\\\\.lt\\\\.d|c\\\\.lt\\\\.s|ceil\\\\.w\\\\.d|ceil\\\\.w\\\\.s|clo|clz|cvt\\\\.d\\\\.s|cvt\\\\.d\\\\.w|cvt\\\\.s\\\\.d|cvt\\\\.s\\\\.w|cvt\\\\.w\\\\.d|cvt\\\\.w\\\\.s|div|div\\\\.d|div\\\\.s|divu|eret|floor\\\\.w\\\\.d|floor\\\\.w\\\\.s|j|jalr??|jr|lbu??|lhu??|ll|lui|lw|lwc1|lwl|lwr|maddu??|mfc0|mfc1|mfhi|mflo|mov\\\\.d|mov\\\\.s|movf|movf\\\\.d|movf\\\\.s|movn|movn\\\\.d|movn\\\\.s|movt|movt\\\\.d|movt\\\\.s|movz|movz\\\\.d|movz\\\\.s|msub|mtc0|mtc1|mthi|mtlo|mul|mul\\\\.d|mul\\\\.s|multu??|neg\\\\.d|neg\\\\.s|nop|nor|ori??|round\\\\.w\\\\.d|round\\\\.w\\\\.s|sb|sc|sdc1|sh|sllv??|slti??|sltiu|sltu|sqrt\\\\.d|sqrt\\\\.s|srav??|srlv??|sub|sub\\\\.d|sub\\\\.s|subu|sw|swc1|swl|swr|syscall|teqi??|tgei??|tgeiu|tgeu|tlti??|tltiu|tltu|trunc\\\\.w\\\\.d|trunc\\\\.w\\\\.s|xori??)\\\\b","name":"support.function.mips"},{"match":"\\\\.(asciiz??|byte|data|double|float|half|kdata|ktext|space|text|word|set\\\\s*(noat|at))\\\\b","name":"storage.type.mips"},{"match":"\\\\.(align|extern||globl)\\\\b","name":"storage.modifier.mips"},{"captures":{"1":{"name":"entity.name.function.label.mips"}},"match":"\\\\b([0-9A-Z_a-z]+):","name":"meta.function.label.mips"},{"captures":{"1":{"name":"punctuation.definition.variable.mips"}},"match":"(\\\\$)([02-9]|1[0-9]|2[0-5]|2[89]|3[01])\\\\b","name":"variable.other.register.usable.by-number.mips"},{"captures":{"1":{"name":"punctuation.definition.variable.mips"}},"match":"(\\\\$)(zero|v[01]|a[0-3]|t[0-9]|s[0-7]|gp|sp|fp|ra)\\\\b","name":"variable.other.register.usable.by-name.mips"},{"captures":{"1":{"name":"punctuation.definition.variable.mips"}},"match":"(\\\\$)(at|k[01]|1|2[67])\\\\b","name":"variable.other.register.reserved.mips"},{"captures":{"1":{"name":"punctuation.definition.variable.mips"}},"match":"(\\\\$)f([0-9]|1[0-9]|2[0-9]|3[01])\\\\b","name":"variable.other.register.usable.floating-point.mips"},{"match":"\\\\b\\\\d+\\\\.\\\\d+\\\\b","name":"constant.numeric.float.mips"},{"match":"\\\\b(\\\\d+|0([Xx])\\\\h+)\\\\b","name":"constant.numeric.integer.mips"},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.mips"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.mips"}},"name":"string.quoted.double.mips","patterns":[{"match":"\\\\\\\\[\\"\\\\\\\\nrt]","name":"constant.character.escape.mips"}]},{"begin":"(^[\\\\t ]+)?(?=#)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.mips"}},"end":"(?!\\\\G)","patterns":[{"begin":"#","beginCaptures":{"0":{"name":"punctuation.definition.comment.mips"}},"end":"\\\\n","name":"comment.line.number-sign.mips"}]}],"scopeName":"source.mips","aliases":["mips"]}')),t=[e];export{t as default};
//# sourceMappingURL=mipsasm.js.map
